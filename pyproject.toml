[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "ordered-set"
authors = [{name = "<PERSON><PERSON>", email = "<EMAIL>"}]
readme = "README.md"
license = {file = "MIT-LICENSE"}
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Programming Language :: Python :: Implementation :: CPython",
    "Programming Language :: Python :: Implementation :: PyPy",
]
dynamic = ["version", "description"]
requires-python = ">=3.9"

[project.urls]
Home = "https://github.com/rspeer/ordered-set"

[project.optional-dependencies]
dev = ["pytest", "black", "pyright"]

[tool.setuptools.dynamic]
version = {attr = "ordered_set.__version__"}
description = {attr = "ordered_set.__doc__"}

[tool.pytest.ini_options]
asyncio_default_fixture_loop_scope = "session"
asyncio_mode = "auto"
